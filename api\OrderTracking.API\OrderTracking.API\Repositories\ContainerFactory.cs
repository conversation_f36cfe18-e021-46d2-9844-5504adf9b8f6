﻿using System;
// Migrated from Google Cloud Firestore to Azure Cosmos DB
// using Google.Cloud.Firestore;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;

namespace OrderTracking.API.Repositories
{
    public sealed class ContainerFactory : IContainerFactory
    {
        #region Fields and Constants

        // Migrated from ValidatingFirestoreClient to ValidatingCosmosClient
        private readonly ValidatingCosmosClient _client;
        private readonly string _database;

        private readonly string _rolesContainer;

        private readonly string _usersContainer;

        #endregion

        #region Constructors

        public ContainerFactory(IConfigurationSection configurationSection, ValidatingCosmosClient cosmosClient)
        {
            if (configurationSection == null) throw new ArgumentNullException(nameof(configurationSection));
            
            //Get the database name and override defaults if appropriate
            _database = configurationSection.GetSection("Database").Value;
            _rolesContainer = configurationSection.GetSection("Roles").Value;
            _usersContainer = configurationSection.GetSection("UserProfiles").Value;

            _client = cosmosClient;
        }

        #endregion

        #region Interface Implementation

        public Container CreateCollection<T>(out string partitionKeyPath) where T : IFirestoreRepository
        {
            //var t = typeof(T);
            //if (typeof(IRolesRepository).IsAssignableFrom(t) || t == typeof(RolesCosmosRepository))
            //{
            //    // Migrated from Firestore to Azure Cosmos DB
            //    return _client.GetContainer(_database, _rolesContainer, out partitionKeyPath);
            //}
            //if (typeof(IUserProfileRepository).IsAssignableFrom(t) || t == typeof(UserProfileCosmosRepository))
            //{
            //    // Migrated from Firestore to Azure Cosmos DB
            //    return _client.GetContainer(_database, _usersContainer, out partitionKeyPath);
            //}

            partitionKeyPath = null;
            return null;
        }

        #endregion
    }
}