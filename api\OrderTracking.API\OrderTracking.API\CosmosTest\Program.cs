﻿using System;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;

namespace CosmosTest
{
    class Program
    {
        static async Task Main(string[] args)
        {
            var endpoint = "https://opt-dev-cosmosdb-001.documents.azure.com:443/";
            var key = "****************************************************************************************";
            var databaseName = "cosmos-clientportalapi-dev-001";

            var options = new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Gateway,
                RequestTimeout = TimeSpan.FromSeconds(30),
                MaxRetryAttemptsOnRateLimitedRequests = 3,
                MaxRetryWaitTimeOnRateLimitedRequests = TimeSpan.FromSeconds(30)
            };

            try
            {
                using var client = new CosmosClient(endpoint, key, options);

                Console.WriteLine("Testing Cosmos DB connection...");
                Console.WriteLine($"Endpoint: {endpoint}");
                Console.WriteLine($"Database: {databaseName}");

                // Try to get the database
                var database = client.GetDatabase(databaseName);
                var response = await database.ReadAsync();

                Console.WriteLine($"✓ Successfully connected to database: {response.Resource.Id}");

                // List containers
                var containerIterator = database.GetContainerQueryIterator<ContainerProperties>();
                Console.WriteLine("\nContainers in database:");

                while (containerIterator.HasMoreResults)
                {
                    var containers = await containerIterator.ReadNextAsync();
                    foreach (var container in containers)
                    {
                        Console.WriteLine($"  - {container.Id}");
                    }
                }

                Console.WriteLine("\n✓ Connection test successful!");
            }
            catch (CosmosException ex)
            {
                Console.WriteLine($"✗ Cosmos DB Error: {ex.StatusCode} - {ex.Message}");
                Console.WriteLine($"  Activity ID: {ex.ActivityId}");
                Console.WriteLine($"  Substatus: {ex.SubStatusCode}");
                if (ex.RetryAfter.HasValue)
                {
                    Console.WriteLine($"  Retry After: {ex.RetryAfter.Value}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ General Error: {ex.Message}");
                Console.WriteLine($"  Stack Trace: {ex.StackTrace}");
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
